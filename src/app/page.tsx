import Link from 'next/link';
import { ThemeToggle } from '@/components/theme/theme-toggle';
import { Button } from '@/components/ui/button';

export default function Home() {
  return (
    <div className="bg-background min-h-screen">
      <div className="absolute top-4 right-4">
        <ThemeToggle />
      </div>

      <div className="container mx-auto px-4 py-16">
        <div className="mx-auto max-w-4xl space-y-8 text-center">
          <div className="space-y-6">
            {/* Kavia.ai Logo and Title */}
            <div className="mb-6 flex items-center justify-center space-x-4">
              <div className="relative">
                <svg
                  width="48"
                  height="48"
                  viewBox="0 0 48 48"
                  fill="none"
                  className="text-primary"
                >
                  {/* Main star shape matching your design */}
                  <path
                    d="M24 4L30 18L44 18L33 28L37 42L24 34L11 42L15 28L4 18L18 18L24 4Z"
                    fill="currentColor"
                  />
                  {/* Center circle */}
                  <circle
                    cx="24"
                    cy="24"
                    r="4"
                    fill="currentColor"
                    opacity="0.8"
                  />
                  {/* Inner details */}
                  <path
                    d="M24 12L26 16L30 16L27 19L28 24L24 22L20 24L21 19L18 16L22 16L24 12Z"
                    fill="currentColor"
                    opacity="0.6"
                  />
                </svg>
              </div>
            </div>
            <h1 className="text-foreground text-4xl font-bold tracking-wide md:text-6xl">
              KAVIA AI
            </h1>
            <h2 className="text-foreground/90 text-2xl font-light md:text-3xl">
              Website UI/UX
            </h2>
            <p className="text-muted-foreground mx-auto max-w-2xl text-lg">
              Multi-tenant AI chatbot platform with beautiful design matching
              kavia.ai branding
            </p>
            <p className="text-muted-foreground border-border mt-6 border-t pt-4 text-sm">
              Last Updated on <span className="underline">July 2025</span>
            </p>
          </div>

          <div className="mt-12 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            <div className="glass-card group hover:glow-sm">
              <h3 className="text-foreground mb-2 text-xl font-semibold">
                Demo Tenant
              </h3>
              <p className="text-muted-foreground mb-4">
                Explore the demo environment with sample projects
              </p>
              <Link href="/demo/projects">
                <Button className="glass-button bg-primary/20 hover:bg-primary/30 w-full border-0 text-white hover:text-white">
                  Enter Demo
                </Button>
              </Link>
            </div>

            <div className="glass-card group hover:glow-sm">
              <h3 className="text-foreground mb-2 text-xl font-semibold">
                Test Tenant
              </h3>
              <p className="text-muted-foreground mb-4">
                Test environment for development and experimentation
              </p>
              <Link href="/test/projects">
                <Button className="glass-button bg-primary/10 hover:bg-primary/20 text-primary hover:text-primary w-full border-0">
                  Enter Test
                </Button>
              </Link>
            </div>

            <div className="glass-card group hover:glow-sm">
              <h3 className="text-foreground mb-2 text-xl font-semibold">
                Theme Demo
              </h3>
              <p className="text-muted-foreground mb-4">
                Showcase of the Kavia.ai theme system
              </p>
              <Link href="/theme-demo">
                <Button className="glass-button bg-accent/20 hover:bg-accent/30 text-accent-foreground w-full border-0">
                  View Themes
                </Button>
              </Link>
            </div>
          </div>

          <div className="mt-16 space-y-6">
            <h2 className="text-foreground text-center text-2xl font-semibold">
              Features
            </h2>
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div className="glass-card">
                <div className="mb-2 flex items-center">
                  <div className="bg-primary mr-3 h-2 w-2 rounded-full"></div>
                  <h3 className="text-foreground font-medium">
                    Kavia.ai Theme System
                  </h3>
                </div>
                <p className="text-muted-foreground text-sm">
                  Beautiful design with orange accents matching the official
                  kavia.ai branding
                </p>
              </div>
              <div className="glass-card">
                <div className="mb-2 flex items-center">
                  <div className="bg-primary mr-3 h-2 w-2 rounded-full"></div>
                  <h3 className="text-foreground font-medium">
                    Multi-Tenant Architecture
                  </h3>
                </div>
                <p className="text-muted-foreground text-sm">
                  Isolated environments for different organizations with secure
                  data separation
                </p>
              </div>
              <div className="glass-card">
                <div className="mb-2 flex items-center">
                  <div className="bg-primary mr-3 h-2 w-2 rounded-full"></div>
                  <h3 className="text-foreground font-medium">
                    Mobile-First Design
                  </h3>
                </div>
                <p className="text-muted-foreground text-sm">
                  Responsive design optimized for all devices and screen sizes
                </p>
              </div>
              <div className="glass-card">
                <div className="mb-2 flex items-center">
                  <div className="bg-primary mr-3 h-2 w-2 rounded-full"></div>
                  <h3 className="text-foreground font-medium">
                    Modern Tech Stack
                  </h3>
                </div>
                <p className="text-muted-foreground text-sm">
                  Next.js 14+, TypeScript, Tailwind CSS with custom Kavia.ai
                  components
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <footer className="border-border mt-16 border-t py-8">
        <div className="container mx-auto px-4 text-center">
          <p className="text-muted-foreground">
            Built with Next.js 14+, TypeScript, Tailwind CSS, and Shadcn/ui
          </p>
        </div>
      </footer>
    </div>
  );
}
