interface ChatPageProps {
  params: Promise<{
    tenant: string;
    project: string;
  }>;
}

import { ThemeToggle } from '@/components/theme/theme-toggle';

export default async function ChatPage({ params }: ChatPageProps) {
  const { tenant, project } = await params;
  return (
    <div className="bg-background flex min-h-screen flex-col">
      {/* Header */}
      <header className="glass border-b border-white/20 px-4 py-3 backdrop-blur-md">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-muted-foreground text-sm">
              {tenant} / {project}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <ThemeToggle />
            <button className="glass-button text-muted-foreground hover:text-foreground border-0 px-3 py-1">
              Settings
            </button>
          </div>
        </div>
      </header>

      {/* Main Chat Area */}
      <div className="flex flex-1">
        {/* Sidebar */}
        <aside className="glass hidden w-64 border-r border-white/20 p-4 md:block">
          <div className="mb-6">
            <h2 className="text-foreground mb-2 font-semibold">
              Project Assets
            </h2>
            <div className="space-y-2">
              <div className="glass-button text-muted-foreground hover:text-foreground w-full border-0 text-left text-sm">
                Codebase
              </div>
              <div className="glass-button text-muted-foreground hover:text-foreground w-full border-0 text-left text-sm">
                Documents
              </div>
              <div className="glass-button text-muted-foreground hover:text-foreground w-full border-0 text-left text-sm">
                Figma Files
              </div>
            </div>
          </div>

          <div>
            <h2 className="text-foreground mb-2 font-semibold">Chat History</h2>
            <div className="space-y-2">
              <div className="glass-button text-muted-foreground hover:text-foreground w-full border-0 text-left text-sm">
                Previous conversation
              </div>
            </div>
          </div>
        </aside>

        {/* Chat Messages */}
        <main className="flex flex-1 flex-col">
          <div className="flex-1 overflow-y-auto p-4">
            <div className="mx-auto max-w-3xl space-y-4">
              <div className="text-muted-foreground text-center">
                Start a conversation with Kavia AI
              </div>
            </div>
          </div>

          {/* Input Area */}
          <div className="glass border-t border-white/20 p-4">
            <div className="mx-auto max-w-3xl">
              <div className="flex space-x-2">
                <input
                  type="text"
                  placeholder="Type your message..."
                  className="glass text-foreground placeholder:text-muted-foreground flex-1 rounded-lg border-0 bg-white/10 px-4 py-2"
                />
                <button className="glass-button bg-primary/20 hover:bg-primary/30 text-primary glow-sm border-0 px-6 py-2">
                  Send
                </button>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
