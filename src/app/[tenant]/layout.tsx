import { ReactNode } from 'react';
import { TenantProvider } from '@/components/tenant/tenant-provider';

interface TenantLayoutProps {
  children: ReactNode;
  params: Promise<{
    tenant: string;
  }>;
}

export default async function TenantLayout({
  children,
  params,
}: TenantLayoutProps) {
  const { tenant } = await params;

  return (
    <TenantProvider tenantSlug={tenant}>
      <div className="bg-background min-h-screen">
        <div className="tenant-context" data-tenant={tenant}>
          {children}
        </div>
      </div>
    </TenantProvider>
  );
}
