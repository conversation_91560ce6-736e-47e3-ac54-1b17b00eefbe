interface ProjectsPageProps {
  params: Promise<{
    tenant: string;
  }>;
}

import { ThemeToggle } from '@/components/theme/theme-toggle';

export default async function ProjectsPage({ params }: ProjectsPageProps) {
  const { tenant } = await params;
  return (
    <div className="bg-background min-h-screen">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8 flex items-center justify-between">
          <div>
            <h1 className="text-foreground mb-2 text-3xl font-bold">
              Chat with Kavia
            </h1>
            <p className="text-muted-foreground">
              Select a project to start chatting - {tenant}
            </p>
          </div>
          <ThemeToggle />
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {/* Placeholder project cards */}
          <div className="glass-card group hover:glow-sm">
            <h3 className="text-foreground mb-2 text-lg font-semibold">
              Sample Project
            </h3>
            <p className="text-muted-foreground mb-4">
              A sample project to get you started with <PERSON><PERSON> AI
            </p>
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground text-sm">
                Last activity: 2 hours ago
              </span>
              <button className="glass-button bg-primary/20 hover:bg-primary/30 text-primary border-0">
                Open Chat
              </button>
            </div>
          </div>

          <div className="glass-card group hover:glow-sm">
            <h3 className="text-foreground mb-2 text-lg font-semibold">
              Web Development
            </h3>
            <p className="text-muted-foreground mb-4">
              Frontend and backend development assistance
            </p>
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground text-sm">
                Last activity: 1 day ago
              </span>
              <button className="glass-button bg-primary/20 hover:bg-primary/30 text-primary border-0">
                Open Chat
              </button>
            </div>
          </div>

          <div className="glass-card group hover:glow-sm">
            <h3 className="text-foreground mb-2 text-lg font-semibold">
              Mobile App
            </h3>
            <p className="text-muted-foreground mb-4">
              React Native and Flutter development
            </p>
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground text-sm">
                Last activity: 3 days ago
              </span>
              <button className="glass-button bg-primary/20 hover:bg-primary/30 text-primary border-0">
                Open Chat
              </button>
            </div>
          </div>
        </div>

        <div className="mt-8">
          <button className="glass-button bg-secondary/20 hover:bg-secondary/30 text-foreground border-0 px-6 py-3">
            + Create New Project
          </button>
        </div>
      </div>
    </div>
  );
}
