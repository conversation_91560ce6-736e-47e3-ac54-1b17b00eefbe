@import 'tailwindcss';

/* Kavia.ai Theme Variables - Matching Your Design */
:root {
  /* Light Theme - Clean White Design */
  --background: 255 255 255; /* Pure white */
  --foreground: 35 31 32; /* Dark text matching your dark theme bg */
  --card: 255 255 255; /* White cards */
  --card-foreground: 35 31 32;
  --popover: 255 255 255;
  --popover-foreground: 35 31 32;

  /* Primary Colors - Kavia.ai Orange Accent */
  --primary: 234 88 12; /* Orange-600 - matching your star icon */
  --primary-foreground: 255 255 255;

  /* Secondary Colors */
  --secondary: 248 250 252; /* Very light gray */
  --secondary-foreground: 35 31 32;

  /* Muted Colors */
  --muted: 241 245 249; /* Light gray */
  --muted-foreground: 100 116 139; /* Medium gray */

  /* Accent Colors - Orange theme */
  --accent: 254 243 199; /* Orange-50 */
  --accent-foreground: 154 52 18; /* Orange-800 */

  /* Destructive Colors */
  --destructive: 239 68 68; /* Red-500 */
  --destructive-foreground: 255 255 255;

  /* Success Colors */
  --success: 34 197 94; /* Green-500 */
  --success-foreground: 255 255 255;

  /* Warning Colors - Orange theme */
  --warning: 245 158 11; /* Amber-500 */
  --warning-foreground: 255 255 255;

  /* Border and Input */
  --border: 229 231 235; /* Gray-200 */
  --input: 229 231 235;
  --ring: 234 88 12; /* Orange-600 */

  /* Chart Colors - Orange theme */
  --chart-1: 234 88 12; /* Orange-600 */
  --chart-2: 251 146 60; /* Orange-400 */
  --chart-3: 253 186 116; /* Orange-300 */
  --chart-4: 254 215 170; /* Orange-200 */
  --chart-5: 254 243 199; /* Orange-50 */

  /* Radius */
  --radius: 0.5rem;
}

/* Dark Theme - Exact Kavia.ai Design Match */
.dark {
  --background: 35 31 32; /* #231f20 - Exact match to your design */
  --foreground: 255 255 255; /* Pure white text */
  --card: 45 41 42; /* Slightly lighter than background for cards */
  --card-foreground: 255 255 255;
  --popover: 45 41 42;
  --popover-foreground: 255 255 255;

  /* Primary Colors - Kavia.ai Orange Accent */
  --primary: 251 146 60; /* Orange-400 - brighter for dark theme */
  --primary-foreground: 35 31 32; /* Dark text on orange */

  /* Secondary Colors */
  --secondary: 55 51 52; /* Slightly lighter than background */
  --secondary-foreground: 255 255 255;

  /* Muted Colors */
  --muted: 55 51 52;
  --muted-foreground: 156 163 175; /* Light gray for secondary text */

  /* Accent Colors - Orange theme */
  --accent: 67 56 202; /* Keep some blue for variety */
  --accent-foreground: 255 255 255;

  /* Destructive Colors */
  --destructive: 248 113 113; /* Red-400 - softer for dark theme */
  --destructive-foreground: 255 255 255;

  /* Success Colors */
  --success: 74 222 128; /* Green-400 - brighter for dark theme */
  --success-foreground: 35 31 32;

  /* Warning Colors - Orange theme */
  --warning: 251 191 36; /* Amber-400 */
  --warning-foreground: 35 31 32;

  /* Border and Input */
  --border: 75 71 72; /* Subtle borders */
  --input: 55 51 52; /* Input backgrounds */
  --ring: 251 146 60; /* Orange focus ring */

  /* Chart Colors - Orange theme */
  --chart-1: 251 146 60; /* Orange-400 */
  --chart-2: 253 186 116; /* Orange-300 */
  --chart-3: 254 215 170; /* Orange-200 */
  --chart-4: 194 65 12; /* Orange-700 */
  --chart-5: 154 52 18; /* Orange-800 */
}

/* System Dark Mode Preference - Uses same colors as .dark class */
@media (prefers-color-scheme: dark) {
  :root:not(.light) {
    --background: 35 31 32;
    --foreground: 255 255 255;
    --card: 45 41 42;
    --card-foreground: 255 255 255;
    --popover: 45 41 42;
    --popover-foreground: 255 255 255;
    --primary: 251 146 60;
    --primary-foreground: 35 31 32;
    --secondary: 55 51 52;
    --secondary-foreground: 255 255 255;
    --muted: 55 51 52;
    --muted-foreground: 156 163 175;
    --accent: 67 56 202;
    --accent-foreground: 255 255 255;
    --destructive: 248 113 113;
    --destructive-foreground: 255 255 255;
    --success: 74 222 128;
    --success-foreground: 35 31 32;
    --warning: 251 191 36;
    --warning-foreground: 35 31 32;
    --border: 75 71 72;
    --input: 55 51 52;
    --ring: 251 146 60;
    --chart-1: 251 146 60;
    --chart-2: 253 186 116;
    --chart-3: 254 215 170;
    --chart-4: 194 65 12;
    --chart-5: 154 52 18;
  }
}

/* Base Styles - Kavia.ai Design */
* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-feature-settings:
    'rlig' 1,
    'calt' 1;
  font-family:
    'Inter',
    system-ui,
    -apple-system,
    sans-serif;
}

/* Subtle background pattern for Kavia.ai - Using CSS custom properties */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(
      circle at 20% 50%,
      hsl(var(--primary) / 0.05) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      hsl(var(--primary) / 0.03) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 80%,
      hsl(var(--accent) / 0.02) 0%,
      transparent 50%
    );
  pointer-events: none;
  z-index: -1;
}

.dark body::before {
  background:
    radial-gradient(
      circle at 20% 50%,
      hsl(var(--primary) / 0.03) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      hsl(var(--primary) / 0.02) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 80%,
      hsl(var(--accent) / 0.01) 0%,
      transparent 50%
    );
}

/* Kavia.ai Components */
.glass {
  backdrop-filter: blur(12px);
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.glass-dark {
  backdrop-filter: blur(12px);
  background-color: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
}

.glass-card {
  backdrop-filter: blur(12px);
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.75rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.glass-card:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.dark .glass-card {
  backdrop-filter: blur(12px);
  background-color: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
}

.dark .glass-card:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.glass-button {
  backdrop-filter: blur(12px);
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  transition: all 0.2s ease;
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.glass-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.glass-button:active {
  transform: scale(0.95);
}

.dark .glass-button {
  backdrop-filter: blur(12px);
  background-color: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
}

.dark .glass-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.glow {
  box-shadow: 0 0 20px hsl(var(--primary) / 0.5);
}

.glow-sm {
  box-shadow: 0 0 10px hsl(var(--primary) / 0.3);
}

/* Mobile-First Responsive Design */
html {
  /* Safe area handling for notched devices */
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  button,
  [role='button'] {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Custom scrollbar with Kavia.ai theme */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background-color: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background-color: hsl(var(--muted-foreground));
  border-radius: 0.125rem;
}

::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--primary));
}
